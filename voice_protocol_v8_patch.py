#!/usr/bin/env python3
"""
Voice Protocol v8 Patch for py-cord
===================================

This script directly applies the Discord voice protocol v8 patches
to fix the 4006 WebSocket error in py-cord installations.

Based on discord.py PR #10210: https://github.com/Rapptz/discord.py/pull/10210
"""

import os
import sys
import shutil
from pathlib import Path

def find_discord_path():
    """Find the discord package path."""
    try:
        import discord
        return Path(discord.__file__).parent
    except ImportError:
        print("ERROR: py-cord not found. Please install it first.")
        sys.exit(1)

def backup_file(file_path):
    """Create backup of a file."""
    backup_path = file_path.with_suffix(file_path.suffix + '.backup')
    if not backup_path.exists():
        shutil.copy2(file_path, backup_path)
        print(f"✓ Backed up {file_path.name}")

def patch_gateway_py(discord_path):
    """Patch gateway.py for voice protocol v8."""
    gateway_file = discord_path / "gateway.py"
    
    if not gateway_file.exists():
        print(f"WARNING: {gateway_file} not found")
        return False
    
    backup_file(gateway_file)
    
    with open(gateway_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Patch 1: Update voice protocol version to v8
    if '"v": 4' in content:
        content = content.replace('"v": 4', '"v": 8')
        print("✓ Updated voice protocol version to v8")
    
    # Patch 2: Add sequence acknowledgment handling
    if 'seq_ack' not in content:
        # Find the VoiceWebSocket class and add seq_ack handling
        voice_ws_pattern = 'class VoiceWebSocket'
        if voice_ws_pattern in content:
            # Add seq_ack initialization
            init_pattern = 'def __init__(self'
            if init_pattern in content:
                # Find the __init__ method in VoiceWebSocket
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'class VoiceWebSocket' in line:
                        # Find the __init__ method after this class
                        for j in range(i, len(lines)):
                            if 'def __init__(self' in lines[j] and 'VoiceWebSocket' in ''.join(lines[max(0, j-10):j]):
                                # Add seq_ack initialization
                                for k in range(j, len(lines)):
                                    if 'self._close_code' in lines[k]:
                                        lines.insert(k+1, '        self._seq_ack = None')
                                        print("✓ Added seq_ack initialization")
                                        break
                                break
                        break
                content = '\n'.join(lines)
    
    # Patch 3: Add resume payload handling
    resume_pattern = 'async def resume(self)'
    if resume_pattern in content and 'seq_ack' not in content:
        # Add seq_ack to resume payload
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'async def resume(self)' in line:
                # Find the resume payload
                for j in range(i, min(i+20, len(lines))):
                    if '"op": 7' in lines[j]:  # Resume opcode
                        # Add seq_ack to the payload
                        for k in range(j, min(j+10, len(lines))):
                            if '}' in lines[k] and 'd' in lines[k-1]:
                                lines[k] = lines[k].replace('}', ', "seq_ack": self._seq_ack}')
                                print("✓ Added seq_ack to resume payload")
                                break
                        break
                break
        content = '\n'.join(lines)
    
    with open(gateway_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def patch_voice_client_py(discord_path):
    """Patch voice_client.py for better endpoint handling."""
    voice_client_file = discord_path / "voice_client.py"
    
    if not voice_client_file.exists():
        print(f"WARNING: {voice_client_file} not found")
        return False
    
    backup_file(voice_client_file)
    
    with open(voice_client_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Patch: Fix endpoint parsing to preserve port information
    old_endpoint_parsing = '''if ':' in endpoint:
            endpoint = endpoint.split(':', 1)[0]'''
    
    if old_endpoint_parsing in content:
        # Remove the port stripping for voice protocol v8
        content = content.replace(old_endpoint_parsing, '# Keep full endpoint with port for voice protocol v8')
        print("✓ Fixed voice endpoint parsing")
    
    # Alternative pattern
    alt_pattern = 'endpoint.split(\':\')[0]'
    if alt_pattern in content:
        content = content.replace(alt_pattern, 'endpoint')
        print("✓ Fixed alternative endpoint parsing")
    
    with open(voice_client_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def patch_voice_state_py(discord_path):
    """Patch voice_state.py for better error handling."""
    voice_state_file = discord_path / "voice_state.py"
    
    if not voice_state_file.exists():
        print(f"WARNING: {voice_state_file} not found")
        return False
    
    backup_file(voice_state_file)
    
    with open(voice_state_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add better handling for 4006, 4021, 4022 errors
    error_codes_pattern = 'if code in (1000, 1001, 1006):'
    if error_codes_pattern in content:
        content = content.replace(
            'if code in (1000, 1001, 1006):',
            'if code in (1000, 1001, 1006, 4006, 4021, 4022):'
        )
        print("✓ Added voice error codes 4006, 4021, 4022 to reconnect logic")
    
    # Alternative pattern
    alt_pattern = 'code == 4006'
    if alt_pattern not in content:
        # Add specific 4006 handling
        reconnect_pattern = 'def _can_reconnect(self'
        if reconnect_pattern in content:
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'def _can_reconnect(self' in line:
                    # Add 4006 handling after the function definition
                    for j in range(i+1, min(i+10, len(lines))):
                        if 'return' in lines[j]:
                            lines.insert(j, '        if code == 4006:  # Session invalid, allow retry')
                            lines.insert(j+1, '            return True')
                            print("✓ Added specific 4006 error handling")
                            break
                    break
            content = '\n'.join(lines)
    
    with open(voice_state_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def check_pynacl():
    """Check PyNaCl version for voice v8 compatibility."""
    try:
        import nacl.secret
        if hasattr(nacl.secret, 'Aead'):
            print("✓ PyNaCl supports voice protocol v8")
            return True
        else:
            print("⚠ PyNaCl may need updating for voice protocol v8")
            print("Run: pip install --upgrade 'PyNaCl>=1.6.0'")
            return False
    except ImportError:
        print("⚠ PyNaCl not found - voice features may not work")
        return False

def main():
    print("Voice Protocol v8 Patch for py-cord")
    print("=" * 40)
    
    discord_path = find_discord_path()
    print(f"Discord package found at: {discord_path}")
    
    success = True
    
    # Apply patches
    print("\nApplying patches...")
    
    if not patch_gateway_py(discord_path):
        success = False
    
    if not patch_voice_client_py(discord_path):
        success = False
    
    if not patch_voice_state_py(discord_path):
        success = False
    
    # Check dependencies
    print("\nChecking dependencies...")
    check_pynacl()
    
    if success:
        print("\n" + "=" * 40)
        print("✅ Voice protocol v8 patch applied successfully!")
        print("\nChanges made:")
        print("• Updated voice protocol from v4 to v8")
        print("• Fixed endpoint parsing for dynamic ports")
        print("• Added better error handling for 4006/4021/4022")
        print("• Added sequence acknowledgment support")
        print("\n🔄 Please restart your bot to apply changes.")
        print("\n📁 Backup files created with .backup extension")
    else:
        print("\n❌ Some patches failed. Check the output above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
