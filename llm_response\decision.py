import logging
import time
import asyncio
import re
import os
import json
from llama_cpp import <PERSON><PERSON><PERSON>
from .config import (
    GEMMA_CPP_MODEL_PATH,
    LLAMA_CPP_N_CTX,
    LLAMA_CPP_N_THREADS,
    LLAMA_CPP_USE_MMAP,
    LLAMA_CPP_USE_MLOCK,
    DECISION_TEMP,
    DECISION_MAX_TOKENS,
    DECISION_CONFIDENCE_THRESHOLD,
    FAST_DECISION_MODE,
    DECISION_TIMEOUT,
    DECISION_MAX_CONTEXT_MESSAGES,
    DECISION_MAX_SPEAKER_HISTORY
)
from .llama_cpp_adapter import extract_response_from_llama_cpp_result
from shared_model import call_model_safe

# Import latency tracking
try:
    from .latency_tracker import mark_latency_timestamp
except ImportError:
    # Fallback if latency tracking isn't available
    def mark_latency_timestamp(event_name):
        pass

logger = logging.getLogger(__name__)

# Global dictionary to store decision context for each channel
# Format: {channel_id: {"last_decision": bool, "conversation_state": str}}
decision_sessions = {}

# Global decision model instance for performance
_decision_model = None

def get_decision_model():
    """Get or create the global decision model instance using RX 6650XT (Device 1)"""
    global _decision_model
    if _decision_model is None:
        logger.info("Initializing decision model (llama.cpp with Vulkan on RX 6650XT)...")
        # Ensure Vulkan backend is set and CUDA is disabled
        os.environ["GGML_BACKEND"] = "vulkan"
        os.environ["CUDA_VISIBLE_DEVICES"] = ""
        os.environ["GGML_CUDA"] = "0"
        # CRITICAL FIX: Make AMD GPU appear as device 0 by only showing it
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "1"  # Only show AMD GPU

        logger.info(f"Environment: GGML_BACKEND={os.environ.get('GGML_BACKEND')}")
        logger.info(f"Environment: GGML_VK_VISIBLE_DEVICES={os.environ.get('GGML_VK_VISIBLE_DEVICES')}")
        logger.info(f"Environment: GGML_VK_DEVICE={os.environ.get('GGML_VK_DEVICE')}")

        _decision_model = Llama(
            model_path=GEMMA_CPP_MODEL_PATH,
            n_ctx=LLAMA_CPP_N_CTX,
            n_gpu_layers=-1,  # Offload all layers to GPU
            main_gpu=0,  # AMD GPU is now device 0 since it's the only visible one
            n_threads=LLAMA_CPP_N_THREADS,
            use_mmap=LLAMA_CPP_USE_MMAP,
            use_mlock=LLAMA_CPP_USE_MLOCK,
            verbose=False  # Disable verbose to reduce startup logs
        )
        logger.info("✅ Decision model initialized successfully with Vulkan backend!")
        logger.info(f"Decision model loaded: {GEMMA_CPP_MODEL_PATH}")
        logger.info("GPU: RX 6650XT (forced as Vulkan Device 0)")

        # Reset environment to show both GPUs for other models
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "0,1"

    return _decision_model

# Legacy LM Studio client code removed - now using llama.cpp directly


async def should_respond(text: str, current_speaker_id: str, conversation_history: list, speaker_turn_history: list, is_currently_speaking: bool, channel_id=None, confidence_threshold=None):
    """LLM-powered decision making, focusing on multi-speaker dynamics and avoiding self-interruption.

    Now with stateful conversation tracking to maintain context across messages and confidence scoring.

    Args:
        text: The message text to analyze
        current_speaker_id: ID of the current speaker
        conversation_history: List of recent conversation messages
        speaker_turn_history: List of recent speaker turns
        is_currently_speaking: Whether Luna is currently speaking
        channel_id: Optional channel ID for stateful tracking
        confidence_threshold: Optional override for the confidence threshold (default: DECISION_CONFIDENCE_THRESHOLD from config)
    """
    mark_latency_timestamp("decision_start")
    
    text_lower = text.lower()
    current_speaker_label = f"User_{current_speaker_id}" # Or fetch display name if available

    # Get or create session for this channel
    session = None
    if channel_id:
        session = decision_sessions.get(channel_id)
        if not session:
            # Initialize a new session
            session = {
                "last_decision": False,
                "conversation_state": "INITIAL"  # Track conversation state: INITIAL, ACTIVE, IDLE
            }
            decision_sessions[channel_id] = session

    # --- Fast Path (Only for direct mentions) ---
    # Only keep the direct mention check as a fast path
    if "luna" in text_lower:
        logger.info("Should respond (Fast Path): Direct mention of Luna.")
        if session:
            session["last_decision"] = True
            session["conversation_state"] = "ACTIVE"
        mark_latency_timestamp("decision_end")
        return True

    # For all other cases, we'll rely on the sophisticated LLM analysis

    # --- Pre-LLM Heuristic: Only track conversation state ---
    if len(speaker_turn_history) >= 2:
        last_speaker = speaker_turn_history[-1].get('display_name', 'Unknown') # Use display_name
        second_last_speaker = speaker_turn_history[-2].get('display_name', 'Unknown') # Use display_name

        # Check if Luna was one of the last two speakers
        luna_in_recent_conversation = last_speaker == "Luna" or second_last_speaker == "Luna"

        # If Luna was one of the last two speakers, we should consider this part of an active conversation
        if luna_in_recent_conversation:
            logger.info("Luna was one of the last two speakers - considering this an active conversation")
            if session:
                session["conversation_state"] = "ACTIVE"

        # We'll let the LLM decide for all other cases

    # --- Note if Luna is speaking (but let the LLM decide) ---
    if is_currently_speaking:
        logger.info("Luna is currently speaking - will let LLM decide if this warrants interruption")

    # --- Optimized Context Processing for Fast Decision Mode ---
    if FAST_DECISION_MODE:
        # Fast mode: Minimal context processing
        luna_in_recent_conversation = any(
            turn.get('display_name') == "Luna" 
            for turn in speaker_turn_history[-DECISION_MAX_SPEAKER_HISTORY:]
        )
        
        # Simple context: Just recent messages  
        context_messages = conversation_history[-DECISION_MAX_CONTEXT_MESSAGES:]
        content_history_str = "\n".join([
            f"{msg.get('role', 'user')}: {msg.get('content', '')[:100]}"  # Truncate long messages
            for msg in context_messages
        ])
        
        # Minimal speaker flow
        speaker_flow_str = " -> ".join([
            turn.get('display_name', 'Unknown') 
            for turn in speaker_turn_history[-DECISION_MAX_SPEAKER_HISTORY:]
        ]) if speaker_turn_history else "No recent turns"
        
        conversation_state_context = f"Active: {luna_in_recent_conversation}"
        
    else:
        # Original extensive context processing (fallback)
        luna_in_recent_conversation = False
        luna_spoke_last = False

        if speaker_turn_history:
            luna_spoke_last = speaker_turn_history[-1].get('display_name') == "Luna"
            for turn in speaker_turn_history[-3:] if len(speaker_turn_history) >= 3 else speaker_turn_history:
                if turn.get('display_name') == "Luna":
                    luna_in_recent_conversation = True
                    break

        if luna_in_recent_conversation and session:
            session["conversation_state"] = "ACTIVE"

        luna_last_message = None
        for msg in reversed(conversation_history):
            if msg.get("role") == "assistant":
                luna_last_message = msg.get("content", "").lower()
                break

        speaker_flow_str = " -> ".join([turn.get('display_name', 'Unknown') for turn in speaker_turn_history]) if speaker_turn_history else "No recent turns"

        recent_messages = []
        luna_involved_messages = []
        for msg in conversation_history:
            if msg.get("role") == "assistant" or "luna" in msg.get("content", "").lower():
                luna_involved_messages.append(msg)
            recent_messages.append(msg)

        context_messages = luna_involved_messages[-5:] if luna_involved_messages else []
        if len(context_messages) < 5:
            additional_needed = 5 - len(context_messages)
            context_messages.extend(recent_messages[-additional_needed:])

        seen = set()
        context_messages = [msg for msg in context_messages if not (msg.get("content") in seen or seen.add(msg.get("content")))]
        content_history_str = "\n".join([f"{msg.get('role', 'user')}: {msg.get('content', '')}" for msg in context_messages])

        conversation_state_context = ""
        if session:
            conversation_state = session.get("conversation_state", "UNKNOWN")
            last_decision = "YES" if session.get("last_decision") else "NO"
            conversation_state_context = f"Conversation State: {conversation_state} | Last Decision: {last_decision}"

    # **OPTIMIZED DECISION PROMPT**
    if FAST_DECISION_MODE:
        # Concise prompt for speed, but with proper decision criteria
        prompt = f"""Analyze this Discord voice message and decide if Luna should respond.

CONTEXT:
Speaker flow: {speaker_flow_str}
Luna recently active: {luna_in_recent_conversation}
Recent messages: {content_history_str}

CURRENT MESSAGE:
{current_speaker_label}: "{text}"

DECISION RULES:
YES - Luna should respond if:
- Luna is mentioned by name
- Message uses 'you'/'your' referring to Luna
- Continuing active conversation with Luna
- Direct question to Luna

NO - Luna should NOT respond if:
- Human-to-human conversation not involving Luna
- Message directed at someone else
- Luna is currently speaking
- Ambient chat not seeking Luna's input

FORMAT: Respond with exactly "YES X%" or "NO X%" where X is confidence 0-100.

Decision:"""
    else:
        # Original detailed prompt (fallback), optimized for llama.cpp
        prompt = f"""You are analyzing a Discord voice chat to determine if Luna should respond.

CONVERSATION CONTEXT:
Recent speaker flow: {speaker_flow_str}
Current speaker: {current_speaker_label}
Luna's current state: {'SPEAKING' if is_currently_speaking else 'IDLE'}
{conversation_state_context}

RECENT CONVERSATION HISTORY:
{content_history_str}

CURRENT MESSAGE:
{current_speaker_label}: "{text}"

RESPONSE CRITERIA:
Luna SHOULD respond if:
• Her name is directly mentioned
• The message is clearly continuing a conversation with her
• The message references something she said previously
• The message contains 'you' or 'your' that contextually refers to Luna
• The message is a question that isn't clearly directed at someone else
• The message seeks an opinion or input that Luna could reasonably provide

Luna should NOT respond if:
• The message is clearly part of a human-to-human exchange not involving Luna
• The message is directed at a specific person other than Luna
• Luna is currently speaking and the message isn't a direct interruption
• The message is ambient conversation not seeking Luna's input

REQUIRED FORMAT: Respond with exactly "YES X%" or "NO X%" where X is confidence 0-100.

Decision:"""

    try:
        # Use the global decision model instance for better performance
        model = get_decision_model()
        mark_latency_timestamp("decision_api_start")
        
        # Add debug logging for the prompt
        logger.debug(f"Decision prompt length: {len(prompt)} characters")
        logger.debug(f"Decision prompt preview: {prompt[:200]}...")
        
        loop = asyncio.get_event_loop()
        def run_inference():
            # Use the shared thread-safe wrapper to avoid concurrent GPU access
            return call_model_safe(
                model,
                prompt,
                max_tokens=DECISION_MAX_TOKENS,
                temperature=DECISION_TEMP,
                top_p=0.9,
                stop=[]  # Removed aggressive stop sequences that were cutting off responses
            )
        
        # Add timeout to prevent hanging
        try:
            result = await asyncio.wait_for(
                loop.run_in_executor(None, run_inference),
                timeout=DECISION_TIMEOUT
            )
        except asyncio.TimeoutError:
            logger.error(f"Decision timeout after {DECISION_TIMEOUT}s")
            # Return a safe fallback
            mark_latency_timestamp("decision_end")
            return "luna" in text_lower
        mark_latency_timestamp("decision_api_end")
        
        # Add debug logging for the raw result
        logger.debug(f"Raw llama.cpp result type: {type(result)}")
        logger.debug(f"Raw llama.cpp result: {str(result)[:500]}...")
        
        decision_text = extract_response_from_llama_cpp_result(result)
        logger.info(f"LLM Decision Raw Output (Vulkan): '{decision_text}'")
        
        # Log the transcription context being analyzed
        logger.info(f"🔍 Decision Analysis Context:")
        logger.info(f"   Speaker: {current_speaker_label}")
        logger.info(f"   Text: '{text}'")
        logger.info(f"   Luna Speaking: {is_currently_speaking}")
        logger.info(f"   Luna Recently Active: {luna_in_recent_conversation}")
        logger.info(f"   Speaker Flow: {speaker_flow_str}")
        if content_history_str:
            logger.info(f"   Recent History: {content_history_str[:150]}{'...' if len(content_history_str) > 150 else ''}")
        else:
            logger.info(f"   Recent History: (empty)")
        logger.info(f"   Prompt Length: {len(prompt)} chars")

        # Parse the decision and confidence with improved regex patterns
        decision_text_upper = decision_text.upper()
        
        # Try multiple parsing patterns to handle different llama.cpp output formats
        decision = None
        confidence = None
        
        # Pattern 1: Standard "YES 85%" or "NO 60%" format
        standard_match = re.search(r'(YES|NO)\s+(\d+)%?', decision_text_upper)
        if standard_match:
            decision = standard_match.group(1)
            confidence = int(standard_match.group(2))
            logger.info(f"Parsed Decision (standard): {decision}, Confidence: {confidence}%")
        else:
            # Pattern 2: Look for "Response:" followed by YES/NO and percentage
            response_match = re.search(r'RESPONSE:\s*(YES|NO)\s*(\d+)%?', decision_text_upper)
            if response_match:
                decision = response_match.group(1)
                confidence = int(response_match.group(2))
                logger.info(f"Parsed Decision (response format): {decision}, Confidence: {confidence}%")
            else:
                # Pattern 3: Look for YES/NO anywhere with optional percentage
                yes_match = re.search(r'YES\s*(\d+)?%?', decision_text_upper)
                no_match = re.search(r'NO\s*(\d+)?%?', decision_text_upper)
                
                if yes_match and no_match:
                    # Both found, take the last one
                    yes_pos = yes_match.start()
                    no_pos = no_match.start()
                    if yes_pos > no_pos:
                        decision = "YES"
                        confidence = int(yes_match.group(1)) if yes_match.group(1) else 75
                    else:
                        decision = "NO"
                        confidence = int(no_match.group(1)) if no_match.group(1) else 75
                elif yes_match:
                    decision = "YES"
                    confidence = int(yes_match.group(1)) if yes_match.group(1) else 75
                elif no_match:
                    decision = "NO"
                    confidence = int(no_match.group(1)) if no_match.group(1) else 75
                else:
                    # Pattern 4: Ultimate fallback - just look for YES/NO keywords
                    if "YES" in decision_text_upper:
                        decision = "YES"
                        confidence = 50  # Low confidence fallback
                    elif "NO" in decision_text_upper:
                        decision = "NO"
                        confidence = 50  # Low confidence fallback
                    
                logger.info(f"Parsed Decision (flexible): {decision}, Confidence: {confidence}%")

        # Final fallback if nothing was parsed
        if decision is None:
            logger.warning(f"Could not parse decision with confidence from: '{decision_text}'")
            decision = "NO"  # Conservative fallback
            confidence = 0   # Zero confidence for unparseable responses
            logger.info(f"Using ultimate fallback decision: {decision}, Confidence: {confidence}%")

        # Use provided threshold or fall back to config value
        current_threshold = confidence_threshold if confidence_threshold is not None else DECISION_CONFIDENCE_THRESHOLD

        # Determine if Luna should respond based on decision and confidence threshold
        should_luna_respond = (decision == "YES" and confidence >= current_threshold)

        # Log the confidence-based decision
        if decision == "YES":
            if confidence >= current_threshold:
                logger.info(f"Luna will respond (Decision: YES, Confidence: {confidence}% ≥ Threshold: {current_threshold}%)")
            else:
                logger.info(f"Luna will NOT respond despite YES decision (Confidence: {confidence}% < Threshold: {current_threshold}%)")
        else:
            logger.info(f"Luna will NOT respond (Decision: NO, Confidence: {confidence}%)")

        # Update session with the decision
        if session:
            session["last_decision"] = should_luna_respond
            if should_luna_respond:
                session["conversation_state"] = "ACTIVE"
            elif session["conversation_state"] == "ACTIVE":
                # Only transition from ACTIVE to IDLE if we've been active
                session["conversation_state"] = "IDLE"

        mark_latency_timestamp("decision_end")
        return should_luna_respond

    except Exception as e:
       logger.error(f"Unexpected error in should_respond LLM logic: {e}", exc_info=True)
       mark_latency_timestamp("decision_end")
       # Safer fallback: Only respond if explicitly mentioned on error
       return "luna" in text_lower