#!/usr/bin/env python3
"""
Discord Voice 4006 Error Fix for py-cord
=========================================

This script applies the Discord.py voice protocol v8 fix to py-cord installations
to resolve the "WebSocket closed with 4006" error that started occurring in July 2025.

The fix upgrades the voice protocol from v4 to v8 and properly handles dynamic ports.

Based on: https://github.com/Rapptz/discord.py/pull/10210
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def find_discord_package():
    """Find the discord package installation directory."""
    try:
        import discord
        discord_path = Path(discord.__file__).parent
        print(f"Found discord package at: {discord_path}")
        return discord_path
    except ImportError:
        print("ERROR: discord package not found. Please install py-cord first.")
        sys.exit(1)

def backup_files(discord_path):
    """Create backup of original files."""
    backup_dir = discord_path.parent / "discord_backup"
    if backup_dir.exists():
        print("Backup already exists, skipping...")
        return backup_dir
    
    print("Creating backup of original files...")
    shutil.copytree(discord_path, backup_dir)
    print(f"Backup created at: {backup_dir}")
    return backup_dir

def apply_voice_fix(discord_path):
    """Apply the voice protocol v8 fix."""
    
    # Fix 1: Update gateway.py for voice protocol v8
    gateway_file = discord_path / "gateway.py"
    if not gateway_file.exists():
        print(f"ERROR: {gateway_file} not found!")
        return False
    
    print("Applying voice protocol v8 fix to gateway.py...")
    
    # Read the current file
    with open(gateway_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Apply the fixes from discord.py PR #10210
    
    # Fix the voice endpoint parsing
    old_endpoint_code = '''def _get_voice_endpoint(self, data):
        endpoint = data.get('endpoint')
        if endpoint is None:
            return None
        
        # Remove the port from the endpoint
        if ':' in endpoint:
            endpoint = endpoint.split(':', 1)[0]
        
        return endpoint'''
    
    new_endpoint_code = '''def _get_voice_endpoint(self, data):
        endpoint = data.get('endpoint')
        if endpoint is None:
            return None
        
        # Keep the port information for voice protocol v8
        return endpoint'''
    
    if old_endpoint_code in content:
        content = content.replace(old_endpoint_code, new_endpoint_code)
        print("✓ Fixed voice endpoint parsing")
    
    # Update voice protocol version
    if 'voice_version = 4' in content:
        content = content.replace('voice_version = 4', 'voice_version = 8')
        print("✓ Updated voice protocol to v8")
    elif '"v": 4' in content:
        content = content.replace('"v": 4', '"v": 8')
        print("✓ Updated voice protocol to v8")
    
    # Add sequence acknowledgment handling
    if 'seq_ack' not in content and 'voice' in content.lower():
        # Find the voice websocket handling section and add seq_ack support
        voice_handler_pattern = 'async def poll_event(self):'
        if voice_handler_pattern in content:
            # Add seq_ack handling after poll_event
            seq_ack_code = '''
        # Handle sequence acknowledgment for voice protocol v8
        if hasattr(self, '_seq_ack') and self._seq_ack is not None:
            await self.send_as_json({'op': 13, 'd': self._seq_ack})
            self._seq_ack = None
'''
            content = content.replace(voice_handler_pattern, voice_handler_pattern + seq_ack_code)
            print("✓ Added sequence acknowledgment handling")
    
    # Write the updated file
    with open(gateway_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def apply_voice_state_fix(discord_path):
    """Apply fixes to voice_state.py."""
    voice_state_file = discord_path / "voice_state.py"
    if not voice_state_file.exists():
        print(f"WARNING: {voice_state_file} not found, skipping voice_state fixes")
        return True
    
    print("Applying voice state fixes...")
    
    with open(voice_state_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add better error handling for 4006 errors
    error_handling_code = '''
    # Handle voice protocol v8 errors
    if code == 4006:  # Session no longer valid
        self._log.info('Voice session invalid (4006), will retry with new session')
        return True  # Allow retry
    elif code in (4021, 4022):  # Voice connection errors
        self._log.info(f'Voice connection error {code}, will retry')
        return True  # Allow retry
'''
    
    # Find where to insert the error handling
    if 'def _can_reconnect' in content and '4006' not in content:
        # Insert after the function definition
        pattern = 'def _can_reconnect(self, code):'
        if pattern in content:
            content = content.replace(pattern, pattern + error_handling_code)
            print("✓ Added voice error handling for 4006, 4021, 4022")
    
    with open(voice_state_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def check_pynacl_version():
    """Check and update PyNaCl if needed for voice v8."""
    try:
        import nacl.secret
        if hasattr(nacl.secret, 'Aead'):
            print("✓ PyNaCl version supports voice protocol v8")
            return True
        else:
            print("WARNING: PyNaCl version may be too old for voice protocol v8")
            print("Attempting to upgrade PyNaCl...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'PyNaCl>=1.6.0'], check=True)
            print("✓ PyNaCl upgraded")
            return True
    except Exception as e:
        print(f"WARNING: Could not check/upgrade PyNaCl: {e}")
        return False

def main():
    print("Discord Voice 4006 Error Fix for py-cord")
    print("=" * 50)
    
    # Find discord package
    discord_path = find_discord_package()
    
    # Create backup
    backup_dir = backup_files(discord_path)
    
    try:
        # Apply fixes
        print("\nApplying voice protocol v8 fixes...")
        
        if not apply_voice_fix(discord_path):
            print("ERROR: Failed to apply voice fixes")
            return False
        
        if not apply_voice_state_fix(discord_path):
            print("ERROR: Failed to apply voice state fixes")
            return False
        
        # Check PyNaCl
        check_pynacl_version()
        
        print("\n" + "=" * 50)
        print("✅ Voice 4006 fix applied successfully!")
        print("\nThe fix includes:")
        print("• Upgraded voice protocol from v4 to v8")
        print("• Fixed voice endpoint parsing for dynamic ports")
        print("• Added sequence acknowledgment handling")
        print("• Improved error handling for 4006/4021/4022 codes")
        print("\nRestart your bot to apply the changes.")
        print(f"\nIf you encounter issues, restore from backup at: {backup_dir}")
        
        return True
        
    except Exception as e:
        print(f"\nERROR: Failed to apply fix: {e}")
        print(f"Restore from backup at: {backup_dir}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
