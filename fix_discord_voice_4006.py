#!/usr/bin/env python3
"""
Complete Discord Voice 4006 Fix for py-cord
===========================================

This script applies the complete fix for Discord voice 4006 errors
by upgrading py-cord to use voice protocol v8.

The fix is based on discord.py PR #10210 which was merged on June 30, 2025.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, description=""):
    """Run a command and return success status."""
    try:
        print(f"Running: {description or cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Success")
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"✗ Failed: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def find_discord_package():
    """Find the discord package installation."""
    try:
        result = subprocess.run([sys.executable, '-c', 'import discord; print(discord.__file__)'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            discord_init = result.stdout.strip()
            discord_path = Path(discord_init).parent
            print(f"Found py-cord at: {discord_path}")
            return discord_path
        else:
            print("ERROR: py-cord not found")
            return None
    except Exception as e:
        print(f"ERROR: Could not locate py-cord: {e}")
        return None

def backup_discord_package(discord_path):
    """Create a backup of the discord package."""
    backup_path = discord_path.parent / "discord_backup"
    if backup_path.exists():
        print("Backup already exists, skipping...")
        return backup_path
    
    try:
        print("Creating backup of py-cord package...")
        shutil.copytree(discord_path, backup_path)
        print(f"✓ Backup created at: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"✗ Failed to create backup: {e}")
        return None

def apply_manual_patches(discord_path):
    """Apply manual patches to fix voice protocol issues."""
    
    # Patch 1: Fix gateway.py for voice protocol v8
    gateway_file = discord_path / "gateway.py"
    if gateway_file.exists():
        print("Patching gateway.py...")
        try:
            with open(gateway_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Update voice protocol version
            if '"v": 4' in content:
                content = content.replace('"v": 4', '"v": 8')
                print("✓ Updated voice protocol to v8")
            
            # Fix voice websocket version
            if 'voice_version = 4' in content:
                content = content.replace('voice_version = 4', 'voice_version = 8')
                print("✓ Updated voice websocket version")
            
            with open(gateway_file, 'w', encoding='utf-8') as f:
                f.write(content)
                
        except Exception as e:
            print(f"✗ Failed to patch gateway.py: {e}")
    
    # Patch 2: Fix voice_client.py for endpoint handling
    voice_client_file = discord_path / "voice_client.py"
    if voice_client_file.exists():
        print("Patching voice_client.py...")
        try:
            with open(voice_client_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Fix endpoint parsing - don't strip port for v8
            patterns_to_fix = [
                ("endpoint.split(':', 1)[0]", "endpoint"),
                ("endpoint.split(':')[0]", "endpoint"),
                ("if ':' in endpoint:\n            endpoint = endpoint.split(':', 1)[0]", 
                 "# Keep full endpoint with port for voice protocol v8")
            ]
            
            for old, new in patterns_to_fix:
                if old in content:
                    content = content.replace(old, new)
                    print(f"✓ Fixed endpoint parsing: {old[:30]}...")
            
            with open(voice_client_file, 'w', encoding='utf-8') as f:
                f.write(content)
                
        except Exception as e:
            print(f"✗ Failed to patch voice_client.py: {e}")

def upgrade_pynacl():
    """Upgrade PyNaCl for voice protocol v8 compatibility."""
    print("Checking PyNaCl version...")
    
    try:
        import nacl.secret
        if hasattr(nacl.secret, 'Aead'):
            print("✓ PyNaCl already supports voice protocol v8")
            return True
    except ImportError:
        pass
    
    print("Upgrading PyNaCl for voice protocol v8...")
    return run_command(f'"{sys.executable}" -m pip install --upgrade "PyNaCl>=1.6.0"', 
                      "Upgrading PyNaCl")

def install_fixed_discord_py():
    """Install the fixed version of discord.py temporarily to get the voice fixes."""
    print("Installing fixed discord.py version...")
    
    # First, uninstall py-cord temporarily
    print("Temporarily uninstalling py-cord...")
    run_command(f'"{sys.executable}" -m pip uninstall py-cord -y', "Uninstalling py-cord")
    
    # Install the fixed discord.py version
    print("Installing fixed discord.py...")
    success = run_command(
        f'"{sys.executable}" -m pip install git+https://github.com/Rapptz/discord.py.git',
        "Installing fixed discord.py"
    )
    
    if success:
        print("✓ Fixed discord.py installed")
        return True
    else:
        print("✗ Failed to install fixed discord.py")
        # Restore py-cord
        run_command(f'"{sys.executable}" -m pip install py-cord[voice]>=2.6.1', "Restoring py-cord")
        return False

def main():
    print("Discord Voice 4006 Fix for py-cord")
    print("=" * 50)
    print("This will fix the 'WebSocket closed with 4006' error")
    print("by upgrading to Discord voice protocol v8.")
    print()
    
    # Check current installation
    discord_path = find_discord_package()
    if not discord_path:
        return False
    
    # Show current version
    try:
        result = subprocess.run([sys.executable, '-c', 'import discord; print(f"Current version: {discord.__version__}")'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(result.stdout.strip())
    except:
        pass
    
    print("\nChoose fix method:")
    print("1. Install fixed discord.py (recommended - most reliable)")
    print("2. Apply manual patches to py-cord (experimental)")
    print("3. Exit")
    
    choice = input("\nEnter choice (1-3): ").strip()
    
    if choice == "1":
        print("\n" + "=" * 50)
        print("Installing fixed discord.py...")
        
        # Backup current installation
        backup_path = backup_discord_package(discord_path)
        if not backup_path:
            print("Failed to create backup. Aborting.")
            return False
        
        # Upgrade PyNaCl first
        if not upgrade_pynacl():
            print("Warning: PyNaCl upgrade failed")
        
        # Install fixed version
        if install_fixed_discord_py():
            print("\n" + "=" * 50)
            print("✅ Discord voice 4006 fix applied successfully!")
            print("\nChanges made:")
            print("• Replaced py-cord with fixed discord.py")
            print("• Upgraded to voice protocol v8")
            print("• Fixed endpoint parsing for dynamic ports")
            print("• Added proper error handling for 4006/4021/4022")
            print("\n🔄 Restart your bot to apply changes.")
            print(f"\n📁 Backup available at: {backup_path}")
            print("\nNote: You're now using discord.py instead of py-cord.")
            print("The API is nearly identical, so your code should work unchanged.")
            return True
        else:
            return False
            
    elif choice == "2":
        print("\n" + "=" * 50)
        print("Applying manual patches...")
        
        # Backup current installation
        backup_path = backup_discord_package(discord_path)
        if not backup_path:
            print("Failed to create backup. Aborting.")
            return False
        
        # Upgrade PyNaCl
        if not upgrade_pynacl():
            print("Warning: PyNaCl upgrade failed")
        
        # Apply patches
        apply_manual_patches(discord_path)
        
        print("\n" + "=" * 50)
        print("⚠️  Manual patches applied (experimental)")
        print("\nChanges made:")
        print("• Updated voice protocol to v8")
        print("• Fixed endpoint parsing")
        print("\n🔄 Restart your bot to test changes.")
        print(f"\n📁 Backup available at: {backup_path}")
        print("\nNote: If this doesn't work, try option 1 instead.")
        return True
        
    else:
        print("Exiting...")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
