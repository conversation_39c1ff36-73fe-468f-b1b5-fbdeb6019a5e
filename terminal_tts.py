import RealtimeTTS
from RealtimeTTS.engines import KokoroEngine 
import time
import sys
import traceback
import torch

# --- Configuration ---
MIXED_VOICE_STRING = '2*af_nicole + 5*zf_xiaoyi' 
INITIAL_VOICE = 'af_nicole' # Base voice for initialization
# --- End Configuration ---

# --- GPU Check ---
print("\n--- Checking PyTorch CUDA Availability ---")
if torch.cuda.is_available():
    gpu_count = torch.cuda.device_count()
    current_device = torch.cuda.current_device()
    gpu_name = torch.cuda.get_device_name(current_device)
    print(f"CUDA is available! Found {gpu_count} GPU(s).")
    print(f"Current CUDA device: {current_device} - {gpu_name}")
else:
    print("CUDA is NOT available. PyTorch will run on CPU.")
print("----------------------------------------\n")
# --- End GPU Check ---

# Initialize engine variable
engine = None

try: 
    # Initialize the KokoroEngine
    print(f"Initializing KokoroEngine with initial voice: {INITIAL_VOICE}...")
    engine = KokoroEngine()  # Initialize with no parameters
    print(f"KokoroEngine initialized.")
    
    # Set the desired mixed voice
    print(f"Setting voice to custom mix: {MIXED_VOICE_STRING}")
    engine.set_voice(MIXED_VOICE_STRING)
    print(f"Engine voice set.")

    # --- Pre-warming (Optional but recommended) ---
    print("Pre-warming engine...")
    try:
        prewarm_stream = RealtimeTTS.TextToAudioStream(engine) 
        prewarm_stream.feed("Engine warm-up.").play(muted=True) 
        time.sleep(0.5) # Allow time for pre-warming
        prewarm_stream.stop() 
        print("Engine pre-warmed.")
    except Exception as e_warm:
        print(f"\n--- Warning: Pre-warming failed: {e_warm} ---\n")
    # --- End Pre-warming ---

    print(f"\nTTS Initialized using KokoroEngine (Mix: {MIXED_VOICE_STRING}).")
    print("Type your text and press Enter to speak.")
    print("Type 'quit' or 'exit' to stop.")
    
    # --- Main Input Loop ---
    while True:
        text = input("> ")
        if text.lower() in ["quit", "exit"]:
            print("Exiting loop...")
            break
        
        if text:
            # print(f"Speaking: {text}") # Optional: Can be removed for cleaner output
            try:
                # Create a new stream instance for each playback
                playback_stream = RealtimeTTS.TextToAudioStream(engine) 
                
                # Feed the text and start playback asynchronously (no waiting)
                # Bypass internal sentence fragmentation for consistency
                tokenizer_bypass = lambda text_input: [text_input] 
                playback_stream.feed([text]).play(tokenize_sentences=tokenizer_bypass) 
                # print("Playback command issued.") # Optional

            except Exception as e_play:
                print(f"\n--- Error during playback ---")
                print(f"Error details: {e_play}")
                traceback.print_exc()
                print(f"-----------------------------\n")

except KeyboardInterrupt:
    print("\nExiting due to KeyboardInterrupt...")
except Exception as e_main:
    print(f"\nAn unexpected error occurred: {e_main}")
    traceback.print_exc()

finally:
    # --- Cleanup ---
    print("Shutting down...")
    try:
        if engine:
            print("Shutting down engine...")
            engine.shutdown()
            print("Engine shut down.")
    except Exception as e_stop_engine:
        print(f"Error shutting down engine: {e_stop_engine}")
        traceback.print_exc()
    print("Script finished.")