"""
Discord Voice Protocol v8 Runtime Patch (Fixed)
===============================================

Import this module to ensure voice protocol v8 is used.
"""

import logging

logger = logging.getLogger(__name__)

try:
    # Patch gateway.py
    from discord import gateway
    
    # Ensure voice protocol v8 is used
    if hasattr(gateway, 'VoiceWebSocket'):
        original_identify = gateway.VoiceWebSocket.send_identify
        
        async def patched_identify(self):
            """Ensure voice protocol v8 is used in identify."""
            logger.info("Using voice protocol v8")
            
            # Create v8 identify payload
            payload = {
                'op': 0,
                'd': {
                    'server_id': str(self.guild_id),
                    'user_id': str(self.user_id),
                    'session_id': self.session_id,
                    'token': self.token,
                    'v': 8  # Force v8
                }
            }
            
            await self.send_as_json(payload)
        
        gateway.VoiceWebSocket.send_identify = patched_identify
        logger.info("✓ Patched VoiceWebSocket.send_identify")
    
    # Patch voice_client.py
    from discord import voice_client
    
    # Check the original connect method signature
    import inspect
    original_connect = voice_client.VoiceClient.connect
    sig = inspect.signature(original_connect)
    
    # Create a compatible patched connect method
    if 'self_deaf' in sig.parameters:
        # discord.py style signature
        async def patched_connect(self, *, timeout=60.0, reconnect=True, self_deaf=False, self_mute=False):
            """Patched connect that preserves endpoint port."""
            original_endpoint = getattr(self, 'endpoint', None)
            if original_endpoint and ':' in original_endpoint:
                logger.info(f"Preserving endpoint with port: {original_endpoint}")
            
            return await original_connect(self, timeout=timeout, reconnect=reconnect, 
                                        self_deaf=self_deaf, self_mute=self_mute)
    else:
        # py-cord style signature
        async def patched_connect(self, *, timeout=60.0, reconnect=True):
            """Patched connect that preserves endpoint port."""
            original_endpoint = getattr(self, 'endpoint', None)
            if original_endpoint and ':' in original_endpoint:
                logger.info(f"Preserving endpoint with port: {original_endpoint}")
            
            return await original_connect(self, timeout=timeout, reconnect=reconnect)
    
    voice_client.VoiceClient.connect = patched_connect
    logger.info("✓ Patched VoiceClient.connect")
    
    print("✅ Discord Voice Protocol v8 Runtime Patch Applied")
    
except Exception as e:
    logger.error(f"Failed to apply voice protocol v8 patch: {e}")
    print(f"❌ Failed to apply voice protocol v8 patch: {e}")
