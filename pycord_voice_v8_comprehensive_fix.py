#!/usr/bin/env python3
"""
Comprehensive Discord Voice Protocol v8 Fix for py-cord
======================================================

This script applies the exact same fixes that were implemented in discord.py PR #10210
to fix the 4006 WebSocket error by upgrading to voice protocol v8.

Based on: https://github.com/Rapptz/discord.py/pull/10210
"""

import os
import sys
import re
import shutil
from pathlib import Path

def find_discord_package():
    """Find the discord package installation directory."""
    try:
        import discord
        discord_path = Path(discord.__file__).parent
        print(f"Found discord package at: {discord_path}")
        return discord_path
    except ImportError:
        print("ERROR: discord package not found. Please install py-cord first.")
        sys.exit(1)

def backup_file(file_path):
    """Create backup of original file."""
    backup_path = file_path.with_suffix(file_path.suffix + '.v8fix_backup')
    if not backup_path.exists():
        shutil.copy2(file_path, backup_path)
        print(f"✓ Backed up {file_path.name}")
    return backup_path

def apply_gateway_fixes(discord_path):
    """Apply voice protocol v8 fixes to gateway.py."""
    gateway_file = discord_path / "gateway.py"
    
    if not gateway_file.exists():
        print(f"ERROR: {gateway_file} not found!")
        return False
    
    backup_file(gateway_file)
    
    with open(gateway_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    changes_made = 0
    
    # Fix 1: Update voice protocol version from v4 to v8
    if "/?v=4" in content:
        content = content.replace("/?v=4", "/?v=8")
        print("✓ Updated voice WebSocket URL to use protocol v8")
        changes_made += 1
    
    # Fix 2: Add sequence acknowledgment handling
    # Look for the voice websocket class and add seq_ack handling
    if "class VoiceWebSocket" in content:
        # Add seq_ack initialization
        if "self._close_code = None" in content:
            content = content.replace(
                "self._close_code = None",
                "self._close_code = None\n        self._seq_ack = None"
            )
            print("✓ Added sequence acknowledgment initialization")
            changes_made += 1
        
        # Add seq_ack handling in resume
        resume_pattern = r"(async def resume\(self\):[^}]+?)(await self\.send_as_json\(payload\))"
        if re.search(resume_pattern, content, re.DOTALL):
            def add_seq_ack(match):
                return match.group(1) + match.group(2) + "\n        if self._seq_ack is not None:\n            await self.send_as_json({'op': 13, 'd': self._seq_ack})"
            
            content = re.sub(resume_pattern, add_seq_ack, content, flags=re.DOTALL)
            print("✓ Added sequence acknowledgment to resume")
            changes_made += 1
    
    # Fix 3: Handle voice protocol v8 identify payload
    identify_pattern = r"('v': )\d+"
    if re.search(identify_pattern, content):
        content = re.sub(identify_pattern, r"\g<1>8", content)
        print("✓ Updated identify payload to use voice protocol v8")
        changes_made += 1
    
    with open(gateway_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✓ Applied {changes_made} fixes to gateway.py")
    return changes_made > 0

def apply_voice_state_fixes(discord_path):
    """Apply voice state fixes for better error handling."""
    voice_state_file = discord_path / "voice_state.py"
    
    if not voice_state_file.exists():
        print(f"WARNING: {voice_state_file} not found, skipping voice_state fixes")
        return True
    
    backup_file(voice_state_file)
    
    with open(voice_state_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    changes_made = 0
    
    # Add better error handling for 4021 and 4022 codes
    if "4006" in content:
        # Look for error handling and add 4021, 4022
        error_pattern = r"(if code == 4006:)"
        if re.search(error_pattern, content):
            content = re.sub(
                error_pattern,
                r"if code in (4006, 4021, 4022):",
                content
            )
            print("✓ Added handling for voice error codes 4021 and 4022")
            changes_made += 1
    
    with open(voice_state_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✓ Applied {changes_made} fixes to voice_state.py")
    return True

def apply_voice_client_fixes(discord_path):
    """Apply voice client fixes for endpoint handling."""
    voice_client_file = discord_path / "voice_client.py"
    
    if not voice_client_file.exists():
        print(f"ERROR: {voice_client_file} not found!")
        return False
    
    backup_file(voice_client_file)
    
    with open(voice_client_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    changes_made = 0
    
    # Fix: Don't strip port from endpoint for voice protocol v8
    # Look for endpoint parsing that strips the port
    port_strip_patterns = [
        r"if ':' in endpoint:\s*endpoint = endpoint\.split\(':', 1\)\[0\]",
        r"endpoint = endpoint\.split\(':', 1\)\[0\]"
    ]
    
    for pattern in port_strip_patterns:
        if re.search(pattern, content):
            content = re.sub(
                pattern,
                "# Keep full endpoint with port for voice protocol v8",
                content
            )
            print("✓ Fixed endpoint parsing to preserve port information")
            changes_made += 1
            break
    
    with open(voice_client_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✓ Applied {changes_made} fixes to voice_client.py")
    return changes_made > 0

def create_runtime_patch(discord_path):
    """Create a runtime patch to ensure all fixes are applied."""
    patch_file = discord_path / "voice_v8_patch.py"
    
    patch_content = '''"""
Discord Voice Protocol v8 Runtime Patch
======================================

This module ensures voice protocol v8 is used and all fixes are applied.
"""

import logging

logger = logging.getLogger(__name__)

try:
    # Import discord modules
    from discord import gateway, voice_client
    import inspect
    
    # Patch 1: Ensure VoiceWebSocket uses v8
    if hasattr(gateway, 'VoiceWebSocket'):
        original_identify = gateway.VoiceWebSocket.send_identify
        
        async def patched_identify(self):
            """Send identify with voice protocol v8."""
            logger.info("Sending voice identify with protocol v8")
            
            payload = {
                'op': 0,
                'd': {
                    'server_id': str(self.guild_id),
                    'user_id': str(self.user_id),
                    'session_id': self.session_id,
                    'token': self.token,
                    'v': 8  # Force voice protocol v8
                }
            }
            
            await self.send_as_json(payload)
        
        gateway.VoiceWebSocket.send_identify = patched_identify
        logger.info("✓ Patched VoiceWebSocket.send_identify for v8")
    
    # Patch 2: Ensure VoiceClient preserves endpoint ports
    if hasattr(voice_client, 'VoiceClient'):
        original_connect = voice_client.VoiceClient.connect
        sig = inspect.signature(original_connect)
        
        # Create compatible connect method based on signature
        if 'self_deaf' in sig.parameters:
            async def patched_connect(self, *, timeout=60.0, reconnect=True, self_deaf=False, self_mute=False):
                logger.info("Connecting with voice protocol v8 (discord.py style)")
                return await original_connect(self, timeout=timeout, reconnect=reconnect, 
                                            self_deaf=self_deaf, self_mute=self_mute)
        else:
            async def patched_connect(self, *, timeout=60.0, reconnect=True):
                logger.info("Connecting with voice protocol v8 (py-cord style)")
                return await original_connect(self, timeout=timeout, reconnect=reconnect)
        
        voice_client.VoiceClient.connect = patched_connect
        logger.info("✓ Patched VoiceClient.connect for v8")
    
    print("✅ Discord Voice Protocol v8 Runtime Patch Applied Successfully!")
    
except Exception as e:
    logger.error(f"Failed to apply voice protocol v8 runtime patch: {e}")
    print(f"❌ Runtime patch failed: {e}")
'''
    
    with open(patch_file, 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print(f"✓ Created runtime patch at {patch_file}")
    return patch_file

def main():
    print("Discord Voice Protocol v8 Comprehensive Fix")
    print("=" * 50)
    print("Based on discord.py PR #10210")
    print()
    
    discord_path = find_discord_package()
    
    print("\nApplying comprehensive voice protocol v8 fixes...")
    
    success = True
    
    # Apply all fixes
    if not apply_gateway_fixes(discord_path):
        print("ERROR: Failed to apply gateway fixes")
        success = False
    
    if not apply_voice_state_fixes(discord_path):
        print("ERROR: Failed to apply voice_state fixes")
        success = False
    
    if not apply_voice_client_fixes(discord_path):
        print("ERROR: Failed to apply voice_client fixes")
        success = False
    
    # Create runtime patch
    patch_file = create_runtime_patch(discord_path)
    
    if success:
        print("\n" + "=" * 50)
        print("✅ Discord voice protocol v8 fix applied successfully!")
        print("\nChanges made:")
        print("• Upgraded voice protocol from v4 to v8")
        print("• Fixed endpoint parsing to preserve port information")
        print("• Added sequence acknowledgment (seq_ack) handling")
        print("• Added support for voice error codes 4021 and 4022")
        print("• Created runtime patch for additional safety")
        
        print(f"\nTo use the runtime patch, add this to your main.py:")
        print("from discord import voice_v8_patch")
        
        print(f"\nBackup files created:")
        print(f"• {discord_path / 'gateway.py.v8fix_backup'}")
        print(f"• {discord_path / 'voice_client.py.v8fix_backup'}")
        print(f"• {discord_path / 'voice_state.py.v8fix_backup'}")
        
        print("\n🎯 Your bot should now be able to connect to Discord voice channels!")
        print("   The 4006 WebSocket error should be resolved.")
    else:
        print("\n❌ Some fixes failed to apply. Check the errors above.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
