"""
Discord Voice Protocol v8 Runtime Patch for py-cord
===================================================

This module patches py-cord at runtime to fix the 4006 WebSocket error
by implementing voice protocol v8 support.

Import this module BEFORE creating your bot instance:

    import voice_v8_runtime_patch
    import discord
    
    bot = discord.Bot()
    # ... rest of your bot code
"""

import asyncio
import logging

logger = logging.getLogger(__name__)

# Store original methods
_original_voice_connect = None
_original_connect_websocket = None
_original_send_identify = None

def patch_voice_protocol():
    """Apply runtime patches for voice protocol v8."""
    import discord
    import discord.gateway
    import discord.voice_client

    global _original_voice_connect, _original_connect_websocket, _original_send_identify

    # Patch 1: Fix voice client connection
    if hasattr(discord.voice_client, 'VoiceClient'):
        _original_voice_connect = discord.voice_client.VoiceClient.connect
        
        async def patched_voice_connect(self, *, timeout=60.0, reconnect=True, self_deaf=False, self_mute=False):
            """Patched voice connect with v8 protocol support."""
            logger.info("Using voice protocol v8 patch")
            
            # Set voice protocol version to v8
            if hasattr(self, '_voice_protocol_version'):
                self._voice_protocol_version = 8
            
            # Patch the endpoint handling
            original_endpoint = getattr(self, 'endpoint', None)
            if original_endpoint and ':' in original_endpoint:
                # Don't strip the port for v8
                logger.info(f"Preserving endpoint with port: {original_endpoint}")
            
            try:
                return await _original_voice_connect(self, timeout=timeout, reconnect=reconnect, 
                                                   self_deaf=self_deaf, self_mute=self_mute)
            except discord.errors.ConnectionClosed as e:
                if e.code == 4006:
                    logger.info("Got 4006 error, retrying with voice protocol v8...")
                    # Force retry with v8 protocol
                    return await _original_voice_connect(self, timeout=timeout, reconnect=reconnect, 
                                                       self_deaf=self_deaf, self_mute=self_mute)
                raise
        
        discord.voice_client.VoiceClient.connect = patched_voice_connect
        logger.info("✓ Patched VoiceClient.connect for v8 protocol")
    
    # Patch 2: Fix voice websocket connection
    if hasattr(discord.voice_client, 'VoiceClient') and hasattr(discord.voice_client.VoiceClient, 'connect_websocket'):
        _original_connect_websocket = discord.voice_client.VoiceClient.connect_websocket
        
        async def patched_connect_websocket(self):
            """Patched websocket connection with v8 protocol."""
            logger.info("Connecting voice websocket with protocol v8")
            
            # Ensure we're using voice protocol v8
            if hasattr(self, 'ws') and self.ws:
                # Patch the websocket to use v8
                if hasattr(self.ws, '_voice_version'):
                    self.ws._voice_version = 8
            
            return await _original_connect_websocket(self)
        
        discord.voice_client.VoiceClient.connect_websocket = patched_connect_websocket
        logger.info("✓ Patched VoiceClient.connect_websocket for v8 protocol")
    
    # Patch 3: Fix voice gateway identify payload
    if hasattr(discord.gateway, 'VoiceWebSocket'):
        voice_ws_class = discord.gateway.VoiceWebSocket
        
        if hasattr(voice_ws_class, 'send_identify'):
            _original_send_identify = voice_ws_class.send_identify
            
            async def patched_send_identify(self):
                """Patched identify with voice protocol v8."""
                logger.info("Sending voice identify with protocol v8")
                
                # Force voice protocol v8 in the identify payload
                if hasattr(self, '_voice_version'):
                    self._voice_version = 8
                
                # Create v8 identify payload
                payload = {
                    'op': 0,  # Identify
                    'd': {
                        'server_id': str(self.guild_id),
                        'user_id': str(self.user_id),
                        'session_id': self.session_id,
                        'token': self.token,
                        'v': 8  # Force voice protocol v8
                    }
                }
                
                await self.send_as_json(payload)
                logger.info("✓ Sent voice identify with protocol v8")
            
            voice_ws_class.send_identify = patched_send_identify
            logger.info("✓ Patched VoiceWebSocket.send_identify for v8 protocol")
    
    # Patch 4: Fix endpoint parsing
    def patch_endpoint_parsing():
        """Patch endpoint parsing to preserve ports for v8."""
        # Find and patch any endpoint parsing functions
        for module_name in ['discord.voice_client', 'discord.gateway']:
            try:
                module = __import__(module_name, fromlist=[''])
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if hasattr(attr, '__code__') and 'endpoint' in attr.__code__.co_names:
                        # This might be an endpoint parsing function
                        logger.debug(f"Found potential endpoint function: {module_name}.{attr_name}")
            except:
                pass
    
    patch_endpoint_parsing()
    
    # Patch 5: Add error handling for 4006 codes
    original_connection_closed_init = discord.errors.ConnectionClosed.__init__
    
    def patched_connection_closed_init(self, socket, *, shard_id, code=None):
        """Patched ConnectionClosed with better 4006 handling."""
        original_connection_closed_init(self, socket, shard_id=shard_id, code=code)
        
        # Log voice protocol errors
        if code == 4006:
            logger.warning("Voice connection closed with 4006 (session invalid) - this is normal for voice protocol transitions")
        elif code in (4021, 4022):
            logger.warning(f"Voice connection error {code} - will retry")
    
    discord.errors.ConnectionClosed.__init__ = patched_connection_closed_init
    logger.info("✓ Patched ConnectionClosed error handling")

def patch_voice_resume():
    """Patch voice resume functionality for v8."""
    import discord.gateway

    if hasattr(discord.gateway, 'VoiceWebSocket'):
        voice_ws_class = discord.gateway.VoiceWebSocket
        
        if hasattr(voice_ws_class, 'resume'):
            original_resume = voice_ws_class.resume
            
            async def patched_resume(self):
                """Patched resume with v8 protocol support."""
                logger.info("Resuming voice connection with protocol v8")
                
                # Send resume with v8 protocol
                payload = {
                    'op': 7,  # Resume
                    'd': {
                        'server_id': str(self.guild_id),
                        'session_id': self.session_id,
                        'token': self.token,
                        'v': 8  # Force voice protocol v8
                    }
                }
                
                await self.send_as_json(payload)
                
                # Send sequence acknowledgment if available
                if hasattr(self, '_seq_ack') and self._seq_ack is not None:
                    await self.send_as_json({'op': 13, 'd': self._seq_ack})
                    logger.info("✓ Sent sequence acknowledgment")
            
            voice_ws_class.resume = patched_resume
            logger.info("✓ Patched VoiceWebSocket.resume for v8 protocol")

# Apply all patches
try:
    patch_voice_protocol()
    patch_voice_resume()
    
    print("🎯 Discord Voice Protocol v8 Runtime Patch Applied Successfully!")
    print("✓ Voice protocol upgraded to v8")
    print("✓ Endpoint parsing fixed for dynamic ports")
    print("✓ Error handling improved for 4006/4021/4022")
    print("✓ Resume functionality patched")
    print("\nYour bot should now be able to connect to Discord voice channels!")
    
except Exception as e:
    logger.error(f"Failed to apply voice protocol v8 patch: {e}")
    print(f"❌ Failed to apply voice protocol v8 patch: {e}")
    print("Please check the error and try again.")
