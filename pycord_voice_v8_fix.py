#!/usr/bin/env python3
"""
Complete py-cord Voice Protocol v8 Fix
======================================

This script applies the complete Discord voice protocol v8 fix specifically
for py-cord to resolve the 4006 WebSocket error.

Based on discord.py PR #10210 but adapted for py-cord's specific implementation.
"""

import os
import sys
import re
import shutil
from pathlib import Path

def find_discord_path():
    """Find the discord package path."""
    try:
        import discord
        return Path(discord.__file__).parent
    except ImportError:
        print("ERROR: py-cord not found")
        sys.exit(1)

def backup_file(file_path):
    """Create backup of a file."""
    backup_path = file_path.with_suffix(file_path.suffix + '.v8fix_backup')
    if not backup_path.exists():
        shutil.copy2(file_path, backup_path)
        print(f"✓ Backed up {file_path.name}")

def fix_gateway_py(discord_path):
    """Apply comprehensive gateway.py fixes for voice protocol v8."""
    gateway_file = discord_path / "gateway.py"
    
    if not gateway_file.exists():
        print(f"ERROR: {gateway_file} not found")
        return False
    
    backup_file(gateway_file)
    
    with open(gateway_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Fix 1: Update voice protocol version to v8
    # Look for voice protocol version patterns
    patterns = [
        (r'"v":\s*4', '"v": 8'),
        (r"'v':\s*4", "'v': 8"),
        (r'voice_version\s*=\s*4', 'voice_version = 8'),
        (r'version.*=.*4.*voice', lambda m: m.group(0).replace('4', '8')),
    ]
    
    for pattern, replacement in patterns:
        if callable(replacement):
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        else:
            content = re.sub(pattern, replacement, content)
    
    # Fix 2: Add sequence acknowledgment handling for voice websocket
    if 'seq_ack' not in content:
        # Find VoiceWebSocket class and add seq_ack support
        voice_ws_pattern = r'class\s+VoiceWebSocket.*?:'
        match = re.search(voice_ws_pattern, content)
        if match:
            # Add seq_ack initialization in __init__
            init_pattern = r'(def\s+__init__\s*\([^)]*\):[^}]*?)(self\._close_code\s*=.*?\n)'
            init_replacement = r'\1\2        self._seq_ack = None\n'
            content = re.sub(init_pattern, init_replacement, content, flags=re.DOTALL)
            
            # Add seq_ack handling in resume
            resume_pattern = r'(async\s+def\s+resume\s*\([^)]*\):.*?)(await\s+self\.send_as_json\s*\(\s*\{[^}]*"op":\s*7[^}]*\}\s*\))'
            if re.search(resume_pattern, content, flags=re.DOTALL):
                resume_replacement = r'\1\2\n        if self._seq_ack is not None:\n            await self.send_as_json({"op": 13, "d": self._seq_ack})'
                content = re.sub(resume_pattern, resume_replacement, content, flags=re.DOTALL)
    
    # Fix 3: Update voice websocket connection handling
    # Look for voice connection patterns and update them
    voice_connect_patterns = [
        # Update any hardcoded voice protocol references
        (r'voice.*protocol.*v4', lambda m: m.group(0).replace('v4', 'v8')),
        (r'voice.*version.*4', lambda m: m.group(0).replace('4', '8')),
    ]
    
    for pattern, replacement in voice_connect_patterns:
        if callable(replacement):
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        else:
            content = re.sub(pattern, replacement, content)
    
    # Fix 4: Add better error handling for 4006 errors
    if '4006' not in content:
        # Find error handling section and add 4006 support
        error_pattern = r'(if\s+code\s+in\s*\([^)]*1006[^)]*\):)'
        if re.search(error_pattern, content):
            error_replacement = r'if code in (1000, 1001, 1006, 4006, 4021, 4022):'
            content = re.sub(error_pattern, error_replacement, content)
    
    if content != original_content:
        with open(gateway_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✓ Applied voice protocol v8 fixes to gateway.py")
        return True
    else:
        print("⚠ No changes needed in gateway.py")
        return True

def fix_voice_client_py(discord_path):
    """Apply voice_client.py fixes for endpoint handling."""
    voice_client_file = discord_path / "voice_client.py"
    
    if not voice_client_file.exists():
        print(f"ERROR: {voice_client_file} not found")
        return False
    
    backup_file(voice_client_file)
    
    with open(voice_client_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # Fix 1: Remove port stripping from endpoints for voice protocol v8
    endpoint_patterns = [
        # Pattern 1: endpoint.split(':')[0]
        (r'endpoint\.split\([\'"][:\'"]\)\[0\]', 'endpoint'),
        # Pattern 2: endpoint.split(':', 1)[0]
        (r'endpoint\.split\([\'"][:\'"]\s*,\s*1\)\[0\]', 'endpoint'),
        # Pattern 3: Multi-line endpoint stripping
        (r'if\s+[\'"][:\'"]\s+in\s+endpoint:\s*\n\s*endpoint\s*=\s*endpoint\.split\([\'"][:\'"]\)[^}]*?\[0\]', 
         '# Keep full endpoint with port for voice protocol v8'),
    ]
    
    for pattern, replacement in endpoint_patterns:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
    
    # Fix 2: Update voice protocol version references
    voice_version_patterns = [
        (r'voice.*version.*4', lambda m: m.group(0).replace('4', '8')),
        (r'"v":\s*4', '"v": 8'),
    ]
    
    for pattern, replacement in voice_version_patterns:
        if callable(replacement):
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        else:
            content = re.sub(pattern, replacement, content)
    
    # Fix 3: Add better error handling for voice connection errors
    if '4006' not in content:
        # Find connection error handling and add voice-specific codes
        error_patterns = [
            (r'(except.*ConnectionClosed.*:)', r'\1\n        # Handle voice protocol v8 errors\n        if hasattr(exc, "code") and exc.code in (4006, 4021, 4022):\n            # Voice session invalid, allow retry\n            pass'),
        ]
        
        for pattern, replacement in error_patterns:
            content = re.sub(pattern, replacement, content, flags=re.DOTALL)
    
    if content != original_content:
        with open(voice_client_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✓ Applied voice endpoint fixes to voice_client.py")
        return True
    else:
        print("⚠ No changes needed in voice_client.py")
        return True

def create_voice_protocol_patch(discord_path):
    """Create a comprehensive voice protocol patch file."""
    patch_file = discord_path / "voice_protocol_v8_patch.py"
    
    patch_content = '''"""
Voice Protocol v8 Runtime Patch for py-cord
===========================================

This module patches py-cord at runtime to support voice protocol v8.
Import this before using voice features.
"""

import discord.gateway
import discord.voice_client

# Patch voice protocol version
original_voice_connect = discord.voice_client.VoiceClient.connect

async def patched_voice_connect(self, *, timeout=60.0, reconnect=True, self_deaf=False, self_mute=False):
    """Patched voice connect with v8 protocol support."""
    # Force voice protocol v8
    if hasattr(self, '_voice_protocol_version'):
        self._voice_protocol_version = 8
    
    # Call original connect
    return await original_voice_connect(self, timeout=timeout, reconnect=reconnect, 
                                      self_deaf=self_deaf, self_mute=self_mute)

# Apply the patch
discord.voice_client.VoiceClient.connect = patched_voice_connect

print("✓ Voice protocol v8 runtime patch applied")
'''
    
    with open(patch_file, 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print(f"✓ Created runtime patch at {patch_file}")
    return patch_file

def main():
    print("py-cord Voice Protocol v8 Fix")
    print("=" * 40)
    
    discord_path = find_discord_path()
    print(f"py-cord found at: {discord_path}")
    
    success = True
    
    # Apply comprehensive fixes
    print("\nApplying voice protocol v8 fixes...")
    
    if not fix_gateway_py(discord_path):
        success = False
    
    if not fix_voice_client_py(discord_path):
        success = False
    
    # Create runtime patch as backup
    patch_file = create_voice_protocol_patch(discord_path)
    
    if success:
        print("\n" + "=" * 40)
        print("✅ py-cord voice protocol v8 fix applied!")
        print("\nChanges made:")
        print("• Updated voice protocol from v4 to v8")
        print("• Fixed endpoint parsing for dynamic ports")
        print("• Added sequence acknowledgment support")
        print("• Improved error handling for 4006/4021/4022")
        print("• Created runtime patch as backup")
        print("\n🔄 Restart your bot to apply changes.")
        print("\n📁 Backup files created with .v8fix_backup extension")
        print(f"\n🔧 Runtime patch available: {patch_file}")
        print("\nIf the fix doesn't work, add this to your bot:")
        print("import sys")
        print(f"sys.path.insert(0, r'{discord_path}')")
        print("import voice_protocol_v8_patch")
    else:
        print("\n❌ Some fixes failed. Check the output above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
