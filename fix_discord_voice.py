#!/usr/bin/env python3
"""
Discord Voice Protocol v8 Fix
=============================

This script fixes the Discord voice 4006 error by patching py-cord
to use voice protocol v8 instead of v4.

Run this script once to apply the fix.
"""

import os
import sys
import re
import shutil
from pathlib import Path

def find_discord_package():
    """Find the discord package installation directory."""
    try:
        import discord
        discord_path = Path(discord.__file__).parent
        print(f"Found discord package at: {discord_path}")
        return discord_path
    except ImportError:
        print("ERROR: discord package not found. Please install py-cord first.")
        sys.exit(1)

def backup_file(file_path):
    """Create backup of original file."""
    backup_path = file_path.with_suffix(file_path.suffix + '.backup')
    if not backup_path.exists():
        shutil.copy2(file_path, backup_path)
        print(f"✓ Backed up {file_path.name}")
    return backup_path

def fix_gateway_py(discord_path):
    """Fix gateway.py for voice protocol v8."""
    gateway_file = discord_path / "gateway.py"
    
    if not gateway_file.exists():
        print(f"ERROR: {gateway_file} not found!")
        return False
    
    backup_file(gateway_file)
    
    with open(gateway_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix 1: Update voice protocol version
    if '"v": 4' in content:
        content = content.replace('"v": 4', '"v": 8')
        print("✓ Updated voice protocol to v8")
    
    # Fix 2: Update voice version
    if "voice_version = 4" in content:
        content = content.replace("voice_version = 4", "voice_version = 8")
        print("✓ Updated voice version to 8")
    
    with open(gateway_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def fix_voice_client_py(discord_path):
    """Fix voice_client.py for endpoint handling."""
    voice_client_file = discord_path / "voice_client.py"
    
    if not voice_client_file.exists():
        print(f"ERROR: {voice_client_file} not found!")
        return False
    
    backup_file(voice_client_file)
    
    with open(voice_client_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix: Remove port stripping for voice protocol v8
    if "if ':' in endpoint:" in content and "endpoint = endpoint.split(':', 1)[0]" in content:
        # Find the pattern and replace it
        pattern = "if ':' in endpoint:\n            endpoint = endpoint.split(':', 1)[0]"
        replacement = "# Keep full endpoint with port for voice protocol v8"
        content = content.replace(pattern, replacement)
        print("✓ Fixed endpoint parsing")
    
    with open(voice_client_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def create_runtime_patch(discord_path):
    """Create a runtime patch file to ensure v8 is used."""
    patch_file = discord_path / "voice_v8_patch.py"
    
    patch_content = '''"""
Discord Voice Protocol v8 Runtime Patch
======================================

Import this module to ensure voice protocol v8 is used.
"""

import logging

logger = logging.getLogger(__name__)

try:
    # Patch gateway.py
    from discord import gateway
    
    # Ensure voice protocol v8 is used
    if hasattr(gateway, 'VoiceWebSocket'):
        original_identify = gateway.VoiceWebSocket.send_identify
        
        async def patched_identify(self):
            """Ensure voice protocol v8 is used in identify."""
            logger.info("Using voice protocol v8")
            
            # Create v8 identify payload
            payload = {
                'op': 0,
                'd': {
                    'server_id': str(self.guild_id),
                    'user_id': str(self.user_id),
                    'session_id': self.session_id,
                    'token': self.token,
                    'v': 8  # Force v8
                }
            }
            
            await self.send_as_json(payload)
        
        gateway.VoiceWebSocket.send_identify = patched_identify
        logger.info("✓ Patched VoiceWebSocket.send_identify")
    
    # Patch voice_client.py
    from discord import voice_client
    
    # Ensure endpoint is not stripped
    original_connect = voice_client.VoiceClient.connect
    
    async def patched_connect(self, *, timeout=60.0, reconnect=True, self_deaf=False, self_mute=False):
        """Patched connect that preserves endpoint port."""
        # Ensure endpoint is not stripped
        original_endpoint = getattr(self, 'endpoint', None)
        if original_endpoint and ':' in original_endpoint:
            logger.info(f"Preserving endpoint with port: {original_endpoint}")
        
        return await original_connect(self, timeout=timeout, reconnect=reconnect, 
                                    self_deaf=self_deaf, self_mute=self_mute)
    
    voice_client.VoiceClient.connect = patched_connect
    logger.info("✓ Patched VoiceClient.connect")
    
    print("✅ Discord Voice Protocol v8 Runtime Patch Applied")
    
except Exception as e:
    logger.error(f"Failed to apply voice protocol v8 patch: {e}")
    print(f"❌ Failed to apply voice protocol v8 patch: {e}")
'''
    
    with open(patch_file, 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print(f"✓ Created runtime patch at {patch_file}")
    return patch_file

def main():
    print("Discord Voice Protocol v8 Fix")
    print("=" * 40)
    
    discord_path = find_discord_package()
    
    # Apply fixes
    print("\nApplying fixes...")
    
    if not fix_gateway_py(discord_path):
        print("ERROR: Failed to fix gateway.py")
        return False
    
    if not fix_voice_client_py(discord_path):
        print("ERROR: Failed to fix voice_client.py")
        return False
    
    # Create runtime patch
    patch_file = create_runtime_patch(discord_path)
    
    print("\n" + "=" * 40)
    print("✅ Discord voice protocol v8 fix applied successfully!")
    print("\nChanges made:")
    print("• Updated voice protocol from v4 to v8")
    print("• Fixed endpoint parsing for dynamic ports")
    print("• Created runtime patch for additional safety")
    
    print("\nTo ensure the fix works, add this to the top of your main.py:")
    print("from discord import voice_v8_patch  # Ensure voice protocol v8")
    
    print("\nIf you encounter issues, restore from backups:")
    print(f"• Gateway: {discord_path / 'gateway.py.backup'}")
    print(f"• Voice Client: {discord_path / 'voice_client.py.backup'}")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
